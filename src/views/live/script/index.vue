<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="题目编码" prop="questionCode">
        <el-input
          v-model="queryParams.questionCode"
          placeholder="请输入题目编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="题干" prop="targetStem">
        <el-input
          v-model="queryParams.targetStem"
          placeholder="请输入题干"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button @click="toggleAdvanced">
          <Icon :icon="showAdvanced ? 'ep:arrow-up' : 'ep:arrow-down'" class="mr-5px" />
          {{ showAdvanced ? '收起' : '展开' }}高级搜索
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['live:script:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-dropdown>
          <el-button type="success" plain>
            <Icon icon="ep:download" class="mr-5px" /> 导出<Icon icon="ep:arrow-down" class="ml-5px" />
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handleExport" v-hasPermi="['live:script:export']">
                <Icon icon="ep:download" class="mr-5px" /> 剧本导出
              </el-dropdown-item>
              <el-dropdown-item @click="handleDialoguesExport" v-hasPermi="['live:script:export']">
                <Icon icon="ep:download" class="mr-5px" /> 剧本对话导出
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown>
          <el-button type="info" plain>
            <Icon icon="ep:upload" class="mr-5px" /> 导入<Icon icon="ep:arrow-down" class="ml-5px" />
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handleImport" v-hasPermi="['live:script:import']">
                <Icon icon="ep:upload" class="mr-5px" /> 剧本导入
              </el-dropdown-item>
              <el-dropdown-item @click="handleDialoguesImport" v-hasPermi="['live:script:import']">
                <Icon icon="ep:upload" class="mr-5px" /> 剧本对话导入
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          type="warning"
          plain
          @click="handleSync"
          :loading="syncLoading"
        >
          <Icon icon="ep:refresh" class="mr-5px" /> 同步数据
        </el-button>
      </el-form-item>
      <!-- 高级搜索选项 -->
      <template v-if="showAdvanced">
        <el-form-item label="标题" prop="title">
          <el-input
            v-model="queryParams.title"
            placeholder="请输入标题"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="学科" prop="subject">
          <el-input
            v-model="queryParams.subject"
            placeholder="请输入学科"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="年级" prop="grade">
          <el-input
            v-model="queryParams.grade"
            placeholder="请输入年级"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="状态" prop="scriptStatus">
          <el-select
            v-model="queryParams.scriptStatus"
            placeholder="请选择状态"
            clearable
            class="!w-240px"
          >
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="queryParams.remark"
            placeholder="请输入备注"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-220px"
          />
        </el-form-item>
      </template>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="题目编码" align="center" prop="questionCode" />
      <el-table-column label="学科" align="center" prop="subject" />
      <el-table-column label="年级" align="center" prop="grade" />
      <el-table-column label="题干" align="center" prop="targetStem" min-width="200" />
      <el-table-column label="题干图片" align="center" width="120">
        <template #default="scope">
          <div class="bg-gray-100 p-5px rounded flex items-center justify-center">
            <el-image
              v-if="scope.row.targetStemImage"
              :src="scope.row.targetStemImage"
              fit="cover"
              class="w-100px h-100px cursor-pointer"
              @click="handlePreview(scope.row.targetStemImage)"
            >
              <template #error>
                <div class="image-slot">
                  <Icon icon="ep:picture" class="text-gray-400" />
                </div>
              </template>
            </el-image>
            <span v-else>-</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="目标答案" align="center" prop="targetAnswer" />
      <el-table-column label="解题方法" align="center" prop="targetMethod" />
      <el-table-column label="扩展属性" align="center" prop="targetExtensions" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="更新时间"
        align="center"
        prop="updateTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="200px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['live:script:update']"
          >
            编辑
          </el-button>
          <router-link :to="'/live/script/dialog/' + scope.row.questionCode">
            <el-button link type="primary">对话记录</el-button>
          </router-link>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['live:script:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ScriptForm ref="formRef" @success="getList" />

  <!-- 图片预览组件 -->
  <el-image-viewer
    v-if="showViewer"
    :url-list="[previewSrc]"
    :initial-index="0"
    :hide-on-click-modal="true"
    teleported
    @close="showViewer = false"
  />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ScriptApi, ScriptVO } from '@/api/live/script'
import { ScriptDialoguesApi } from '@/api/live/scriptdialogues'
import ScriptForm from './ScriptForm.vue'
import { useRouter } from 'vue-router'
import { ElImageViewer } from 'element-plus'

/** 剧本信息表，主要存储已经录入的题目信息 列表 */
defineOptions({ name: 'Script' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter() // 路由

const loading = ref(true) // 列表的加载中
const list = ref<ScriptVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  description: undefined,
  subject: undefined,
  grade: undefined,
  appType: undefined,
  questionType: undefined,
  difficultyLevel: undefined,
  scriptStatus: undefined,
  scriptType: undefined,
  keywords: undefined,
  version: undefined,
  remark: undefined,
  createTime: [],
  questionCode: undefined,
  targetStem: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const dialoguesExportLoading = ref(false) // 剧本对话导出的加载中
const syncLoading = ref(false) // 同步按钮的加载状态
const showAdvanced = ref(false) // 是否显示高级搜索

// 图片预览相关
const showViewer = ref(false)
const previewSrc = ref('')

// 自定义图片预览
const handlePreview = (url: string) => {
  previewSrc.value = url
  showViewer.value = true
}

// 在 script setup 部分添加导入相关代码
const importLoading = ref(false) // 导入的加载中
const dialoguesImportLoading = ref(false) // 剧本对话导入的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ScriptApi.getScriptPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ScriptApi.deleteScript(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    const confirmResult = await message.confirm('是否确认导出剧本数据？')
    if (!confirmResult) {
      return
    }
    // 发起导出
    exportLoading.value = true
    const data = await ScriptApi.exportScript(queryParams)
    download.excel(data, '剧本信息表.xls')
  } catch (error: any) {
    message.error('导出失败：' + (error.message || '未知错误'))
  } finally {
    exportLoading.value = false
  }
}

/** 剧本对话导出按钮操作 */
const handleDialoguesExport = async () => {
  try {
    // 导出的二次确认
    const confirmResult = await message.confirm('是否确认导出剧本对话数据？')
    if (!confirmResult) {
      return
    }
    // 发起导出
    dialoguesExportLoading.value = true
    const data = await ScriptDialoguesApi.exportScriptDialogues(queryParams)
    download.excel(data, '剧本对话信息表.xls')
  } catch (error: any) {
    message.error('剧本对话导出失败：' + (error.message || '未知错误'))
  } finally {
    dialoguesExportLoading.value = false
  }
}

/** 同步数据操作 */
const handleSync = async () => {
  try {
    // 二次确认
    await message.confirm('是否确认同步数据？')
    // 开始同步
    syncLoading.value = true
    await ScriptApi.syncSo()
    message.success(t('common.syncSuccess'))
    // 刷新列表
    await getList()
  } catch (error: any) {
    if (error) {
      message.error('同步失败：' + (error.message || '未知错误'))
    }
  } finally {
    syncLoading.value = false
  }
}

/** 切换高级搜索 */
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

/** 导入按钮操作 */
const handleImport = () => {
  // 创建文件上传的 input 元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls'
  input.onchange = async (e: Event) => {
    const target = e.target as HTMLInputElement
    if (!target.files?.length) return
    
    try {
      importLoading.value = true
      await ScriptApi.importScriptExcel(target.files[0])
      message.success('导入成功')
      // 刷新列表
      await getList()
    } catch (error: any) {
      message.error('导入失败：' + (error.message || '未知错误'))
    } finally {
      importLoading.value = false
      // 清空 input 的值，以便可以重复选择同一个文件
      input.value = ''
    }
  }
  input.click()
}

/** 剧本对话导入按钮操作 */
const handleDialoguesImport = () => {
  // 创建文件上传的 input 元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls'
  input.onchange = async (e: Event) => {
    const target = e.target as HTMLInputElement
    if (!target.files?.length) return
    
    try {
      dialoguesImportLoading.value = true
      await ScriptDialoguesApi.importScriptDialoguesExcel(target.files[0])
      message.success('剧本对话导入成功')
    } catch (error: any) {
      message.error('剧本对话导入失败：' + (error.message || '未知错误'))
    } finally {
      dialoguesImportLoading.value = false
      // 清空 input 的值，以便可以重复选择同一个文件
      input.value = ''
    }
  }
  input.click()
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style>
:root {
  --el-image-viewer-mask-color: rgba(0, 0, 0, 0.8);
}
</style>