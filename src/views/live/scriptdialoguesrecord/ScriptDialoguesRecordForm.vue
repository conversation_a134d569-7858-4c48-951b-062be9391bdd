<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="表live_script的 id" prop="liveScriptId">
        <el-input v-model="formData.liveScriptId" placeholder="请输入表live_script的 id" />
      </el-form-item>
      <el-form-item label="表live_script_dialogues 的id" prop="liveScriptDialoguesId">
        <el-input
          v-model="formData.liveScriptDialoguesId"
          placeholder="请输入表live_script_dialogues 的id"
        />
      </el-form-item>
      <el-form-item label="对话轮次" prop="roundNumber">
        <el-input v-model="formData.roundNumber" placeholder="请输入对话轮次" />
      </el-form-item>
      <el-form-item label="学生提问" prop="studentRequest">
        <el-input v-model="formData.studentRequest" placeholder="请输入学生提问" />
      </el-form-item>
      <el-form-item label="热词" prop="hotwords">
        <el-input v-model="formData.hotwords" placeholder="请输入热词，多个热词用逗号分隔" />
      </el-form-item>
      <el-form-item label="老师回应内容" prop="teacherResponse">
        <el-input v-model="formData.teacherResponse" placeholder="请输入老师回应内容" />
      </el-form-item>
      <el-form-item label="老师回应板书内容" prop="teacherResponseBlackboard">
        <el-input
          v-model="formData.teacherResponseBlackboard"
          placeholder="请输入老师回应板书内容"
        />
      </el-form-item>
      <el-form-item label="老师回应TTS语音内容" prop="teacherResponseTtsContent">
        <Editor v-model="formData.teacherResponseTtsContent" height="150px" />
      </el-form-item>
      <el-form-item label="老师回应TTS音频地址" prop="teacherResponseTtsUrl">
        <el-input
          v-model="formData.teacherResponseTtsUrl"
          placeholder="请输入老师回应TTS音频地址"
        />
      </el-form-item>
      <el-form-item label="房间id" prop="roomId">
        <el-input v-model="formData.roomId" placeholder="请输入房间id" />
      </el-form-item>
      <el-form-item label="轮次ID" prop="roundId">
        <el-input v-model="formData.roundId" placeholder="请输入轮次ID" />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="formData.userId" placeholder="请输入用户ID" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ScriptDialoguesRecordApi, ScriptDialoguesRecordVO } from '@/api/live/scriptdialoguesrecord'

/** 用户剧本调用记录 表单 */
defineOptions({ name: 'ScriptDialoguesRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  liveScriptId: undefined,
  liveScriptDialoguesId: undefined,
  roundNumber: undefined,
  studentRequest: undefined,
  teacherResponse: undefined,
  teacherResponseBlackboard: undefined,
  teacherResponseTtsContent: undefined,
  teacherResponseTtsUrl: undefined,
  hotwords: undefined,
  roomId: undefined,
  roundId: undefined,
  userId: undefined,
  remark: undefined
})
const formRules = reactive({
  liveScriptId: [{ required: true, message: '表live_script的 id不能为空', trigger: 'blur' }],
  liveScriptDialoguesId: [
    { required: true, message: '表live_script_dialogues 的id不能为空', trigger: 'blur' }
  ]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ScriptDialoguesRecordApi.getScriptDialoguesRecord(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ScriptDialoguesRecordVO
    if (formType.value === 'create') {
      await ScriptDialoguesRecordApi.createScriptDialoguesRecord(data)
      message.success(t('common.createSuccess'))
    } else {
      await ScriptDialoguesRecordApi.updateScriptDialoguesRecord(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    liveScriptId: undefined,
    liveScriptDialoguesId: undefined,
    roundNumber: undefined,
    studentRequest: undefined,
    teacherResponse: undefined,
    teacherResponseBlackboard: undefined,
    teacherResponseTtsContent: undefined,
    teacherResponseTtsUrl: undefined,
    hotwords: undefined,
    roomId: undefined,
    roundId: undefined,
    userId: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>
