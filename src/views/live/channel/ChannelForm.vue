<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="700px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="渠道名称" prop="channelName">
        <el-input v-model="formData.channelName" placeholder="请输入渠道名称" />
      </el-form-item>
      <el-form-item label="渠道类型" prop="channelType">
        <el-select v-model="formData.channelType" placeholder="请选择渠道类型" class="!w-full">
          <el-option
            v-for="item in channelTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!--      <el-form-item label="渠道配置信息" prop="channelConfig">-->
      <!--        <el-input v-model="formData.channelConfig" placeholder="请输入渠道配置信息" />-->
      <!--      </el-form-item>-->

      <!-- 绑定设备列表 -->
      <el-form-item label="绑定设备">
        <div class="mb-2 flex justify-end">
          <el-button type="primary" @click="openAddDeviceDialog" size="small">添加设备</el-button>
        </div>
        <el-table :data="formData.boundDevices || []" border stripe size="small">
          <el-table-column label="设备SN号" prop="deviceSn" min-width="180" />
          <el-table-column label="操作" width="100" align="center">
            <template #default="{ $index }">
              <el-button link type="danger" @click="removeDevice($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>

    <!-- 添加设备弹窗 -->
    <el-dialog v-model="deviceDialogVisible" title="添加设备" append-to-body width="500px">
      <el-form :model="deviceForm" ref="deviceFormRef" label-width="100px">
        <el-form-item
          label="设备SN号"
          prop="deviceSn"
          :rules="[{ required: true, message: '请输入设备SN号', trigger: 'blur' }]"
        >
          <el-input v-model="deviceForm.deviceSn" placeholder="请输入设备SN号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="deviceDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addDevice">确定</el-button>
      </template>
    </el-dialog>

    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ChannelApi, ChannelVO, ChannelDeviceRespVO } from '@/api/live/channel'
import { ChannelDeviceApi } from '@/api/live/channeldevice'

/** 渠道信息 表单 */
defineOptions({ name: 'ChannelForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
// 渠道类型选项
const channelTypeOptions = [
  { value: 'LIVE', label: '直播渠道' },
  { value: 'TALENT', label: '达人渠道' }
]

// 设备弹窗相关
const deviceDialogVisible = ref(false)
const deviceForm = ref({
  deviceSn: ''
})
const deviceFormRef = ref()

const formData = ref({
  id: undefined,
  channelName: undefined,
  channelType: undefined,
  channelConfig: undefined,
  boundDevices: [] as ChannelDeviceRespVO[]
})
const formRules = reactive({
  channelName: [{ required: true, message: '渠道名称不能为空', trigger: 'blur' }],
  channelType: [{ required: true, message: '渠道类型不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开添加设备弹窗 */
const openAddDeviceDialog = () => {
  deviceDialogVisible.value = true
  deviceForm.value.deviceSn = ''
}

/** 添加设备 */
const addDevice = async () => {
  await deviceFormRef.value.validate()

  // 检查是否已存在相同的设备SN
  const exists = formData.value.boundDevices?.some(
    (device) => device.deviceSn === deviceForm.value.deviceSn
  )
  if (exists) {
    message.warning('该设备已绑定到当前渠道')
    return
  }

  // 添加到列表
  if (!formData.value.boundDevices) {
    formData.value.boundDevices = []
  }

  formData.value.boundDevices.push({
    channelId: formData.value.id || 0, // 如果是新建渠道，先用 0 占位，后续会更新
    channelCode: '', // 这个字段在后端会自动生成
    deviceSn: deviceForm.value.deviceSn
  })

  deviceDialogVisible.value = false
  message.success('添加设备成功')
}

/** 移除设备 */
const removeDevice = (index: number) => {
  formData.value.boundDevices?.splice(index, 1)
  message.success('移除设备成功')
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ChannelApi.getChannel(id)
      // 确保 boundDevices 存在
      if (!formData.value.boundDevices) {
        formData.value.boundDevices = []
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ChannelVO
    let channelId: number

    // 先保存渠道信息
    if (formType.value === 'create') {
      // 新建渠道
      const result = await ChannelApi.createChannel(data)
      channelId = result
      message.success(t('common.createSuccess'))

      // 如果有绑定设备，创建设备关联
      if (data.boundDevices && data.boundDevices.length > 0) {
        // 获取创建后的渠道详情，包含 channelCode
        const channelDetail = await ChannelApi.getChannel(channelId)
        const channelCode = channelDetail.channelCode || ''

        // 更新设备关联的渠道ID和编码
        data.boundDevices.forEach((device) => {
          device.channelId = channelId
          device.channelCode = channelCode
        })

        // 创建新的设备关联
        for (const device of data.boundDevices) {
          await ChannelDeviceApi.createChannelDevice({
            channelId: channelId,
            channelCode: channelCode,
            deviceSn: device.deviceSn
          })
        }
      }
      // 如果没有绑定设备，不需要处理
    } else {
      // 编辑渠道
      await ChannelApi.updateChannel(data)
      channelId = data.id
      message.success(t('common.updateSuccess'))

      // 查询当前渠道绑定的设备
      const response = await ChannelDeviceApi.getChannelDevicePage({
        pageNo: 1,
        pageSize: 100,
        channelId: channelId
      })
      const currentDevices = response.list || []

      // 获取渠道编码
      const channelDetail = await ChannelApi.getChannel(channelId)
      let channelCode = channelDetail.channelCode || ''

      if (data.boundDevices && data.boundDevices.length > 0) {
        // 有绑定设备，需要更新

        // 找出需要删除的设备
        for (const currentDevice of currentDevices) {
          // 如果当前设备不在新的设备列表中，则删除
          const stillExists = data.boundDevices.some(
            (device) => device.deviceSn === currentDevice.deviceSn
          )
          if (!stillExists) {
            await ChannelDeviceApi.deleteChannelDevice(currentDevice.id)
          }
        }

        // 找出需要新增的设备
        for (const newDevice of data.boundDevices) {
          // 如果新设备不在当前设备列表中，则新增
          const alreadyExists = currentDevices.some(
            (device) => device.deviceSn === newDevice.deviceSn
          )
          if (!alreadyExists) {
            await ChannelDeviceApi.createChannelDevice({
              channelId: channelId,
              channelCode: channelCode,
              deviceSn: newDevice.deviceSn
            })
          }
        }
      } else {
        // 没有绑定设备，需要删除所有关联
        for (const device of currentDevices) {
          await ChannelDeviceApi.deleteChannelDevice(device.id)
        }
      }
    }

    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    channelName: undefined,
    channelType: undefined,
    channelConfig: undefined,
    boundDevices: []
  }
  formRef.value?.resetFields()
}
</script>
