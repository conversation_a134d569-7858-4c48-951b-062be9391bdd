<template>
  <Dialog title="审核详情" v-model="dialogVisible" width="930px">
    <!-- 稍微增加宽度 -->
    <!-- 基本信息 -->
    <el-descriptions v-if="detailData" :column="2" border class="mb-4">
      <el-descriptions-item label="主键ID">{{ detailData.id }}</el-descriptions-item>
      <el-descriptions-item label="达人昵称">{{ detailData.talentName }}</el-descriptions-item>
      <el-descriptions-item label="剧本版本">{{ detailData.scriptVersion }}</el-descriptions-item>
      <el-descriptions-item label="剧本文档">
        <el-link
          v-if="detailData.documentUrl"
          type="primary"
          :href="detailData.documentUrl"
          target="_blank"
        >
          点击下载/查看
        </el-link>
        <span v-else>无</span>
      </el-descriptions-item>
      <el-descriptions-item label="审核时间">
        {{ detailData.auditTime ? dateFormatter(detailData, null, detailData.auditTime) : 'N/A' }}
      </el-descriptions-item>
      <el-descriptions-item label="审核状态">
        <el-tag :type="getStatusTagType(detailData.status)">
          {{ formatStatus(detailData.status) }}
        </el-tag>
      </el-descriptions-item>
      <!--      <el-descriptions-item label="审核意见" :span="2">{{ detailData.auditOpinion || '无' }}</el-descriptions-item>-->
      <!--      <el-descriptions-item label="创建时间">-->
      <!--        {{ dateFormatter(detailData, null, detailData.createTime) }}-->
      <!--      </el-descriptions-item>-->
    </el-descriptions>

    <!-- 品牌规范审核结果 -->
    <el-divider content-position="left">品牌规范</el-divider>
    <el-table
      v-if="brandRules.length > 0"
      :data="brandRules"
      border
      stripe
      class="mb-4"
      style="width: 100%"
    >
      <!-- 调整列的 min-width 以优化布局 -->
      <el-table-column label="类别" prop="category" align="center" width="80" />
      <el-table-column label="规范书写" prop="spelling_requirement" align="center" width="120" />
      <el-table-column label="是否合规" prop="spelling_met" align="center" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.spelling_met === 1 ? 'success' : 'danger'">
            {{ scope.row.spelling_met === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="不合规问题"
        prop="spelling_issues"
        align="center"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column label="频次要求" prop="frequency_requirement" align="center" width="120" />
      <el-table-column label="口播频次" prop="frequency_lines" align="center" width="70" />
      <el-table-column label="花字频次" prop="frequency_supers" align="center" width="70" />
      <el-table-column label="是否合规" prop="frequency_met" align="center" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.frequency_met === 1 ? 'success' : 'danger'">
            {{ scope.row.frequency_met === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="不合规问题"
        prop="frequency_issues"
        align="center"
        width="120"
        show-overflow-tooltip
      />
    </el-table>
    <!--    <div v-if="brandRules.length > 0" class="mb-4">-->
    <!--      <el-table v-for="(rule, index) in brandRules" :key="index" :data="[rule]" border stripe class="mb-2">-->
    <!--        <el-table-column label="类别" width="120">-->
    <!--          <template #default>-->
    <!--            <span>{{ rule.category }}</span>-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <!--        <el-table-column label="规范书写" width="200">-->
    <!--          <template #default>-->
    <!--            <div class="rule-content">{{ rule.spelling_requirement }}</div>-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <!--        <el-table-column label="是否合规" width="100">-->
    <!--          <template #default>-->
    <!--            <el-tag :type="rule.spelling_met === 1 ? 'success' : 'danger'">-->
    <!--              {{ rule.spelling_met === 1 ? '是' : '否' }}-->
    <!--            </el-tag>-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <!--        <el-table-column label="不合规问题" width="200">-->
    <!--          <template #default>-->
    <!--            <div class="rule-content">{{ rule.spelling_issues || '-' }}</div>-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <!--      </el-table>-->

    <!--      <el-table v-for="(rule, index) in brandRules" :key="'freq-'+index" :data="[rule]" border stripe class="mb-2">-->
    <!--        <el-table-column label="类别" width="120">-->
    <!--          <template #default>-->
    <!--            <span>{{ rule.category }}</span>-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <!--        <el-table-column label="频次要求" width="200">-->
    <!--          <template #default>-->
    <!--            <div class="rule-content">{{ rule.frequency_requirement }}</div>-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <!--        <el-table-column label="口播频次" width="100">-->
    <!--          <template #default>-->
    <!--            <span>{{ rule.frequency_lines }}</span>-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <!--        <el-table-column label="花字频次" width="100">-->
    <!--          <template #default>-->
    <!--            <span>{{ rule.frequency_supers }}</span>-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <!--        <el-table-column label="是否合规" width="100">-->
    <!--          <template #default>-->
    <!--            <el-tag :type="rule.frequency_met === 1 ? 'success' : 'danger'">-->
    <!--              {{ rule.frequency_met === 1 ? '是' : '否' }}-->
    <!--            </el-tag>-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <!--        <el-table-column label="不合规问题" width="200">-->
    <!--          <template #default>-->
    <!--            <div class="rule-content">{{ rule.frequency_issues || '-' }}</div>-->
    <!--          </template>-->
    <!--        </el-table-column>-->
    <!--      </el-table>-->
    <!--    </div>-->
    <el-empty v-else description="无品牌规范审核结果" class="mb-4" />

    <!-- 产品展示审核结果 -->
    <el-divider content-position="left">产品展示</el-divider>
    <el-descriptions v-if="productDisplayResult" :column="1" border>
      <!-- 更新标签以匹配截图 -->
      <el-descriptions-item label="故事主题">
        <el-tag :type="productDisplayResult.story_theme === 1 ? 'success' : 'danger'">
          {{ productDisplayResult.story_theme === 1 ? '合规' : '不合规' }}
        </el-tag>
        <p class="mt-1 text-sm text-gray-600">{{ productDisplayResult.story_theme_issue }}</p>
      </el-descriptions-item>
      <el-descriptions-item label="同题索引">
        <el-tag :type="productDisplayResult.same_quiz === 1 ? 'success' : 'danger'">
          {{ productDisplayResult.same_quiz === 1 ? '合规' : '不合规' }}
        </el-tag>
        <p class="mt-1 text-sm text-gray-600">{{ productDisplayResult.same_quiz_issue }}</p>
      </el-descriptions-item>
      <el-descriptions-item label="产品演示篇幅">
        <el-tag :type="productDisplayResult.product_length === 1 ? 'success' : 'danger'">
          {{ productDisplayResult.product_length === 1 ? '合规' : '不合规' }}
        </el-tag>
        <p class="mt-1 text-sm text-gray-600">{{ productDisplayResult.product_length_issue }}</p>
      </el-descriptions-item>
      <el-descriptions-item label="寒雪接梗">
        <el-tag :type="productDisplayResult.joking_teacher === 1 ? 'success' : 'danger'">
          {{ productDisplayResult.joking_teacher === 1 ? '合规' : '不合规' }}
        </el-tag>
        <p class="mt-1 text-sm text-gray-600">{{ productDisplayResult.joking_teacher_issue }}</p>
      </el-descriptions-item>
    </el-descriptions>
    <el-empty v-else description="无产品展示审核结果" />

    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue' // 移除了 computed，因为未使用
import { ScriptAuditVO } from '@/api/live/scriptaudit'
import { dateFormatter } from '@/utils/formatTime'

// 定义品牌规范审核结果的接口类型
interface BrandRule {
  category: string
  spelling_requirement: string
  spelling_met: number // 0: 不合规, 1: 合规
  spelling_issues: string
  frequency_requirement: string
  frequency_lines: number
  frequency_supers: number
  frequency_met: number // 0: 不合规, 1: 合规
  frequency_issues: string
}

// 定义产品展示审核结果的接口类型
interface ProductDisplayResult {
  story_theme_issue: string
  story_theme: number // 0: 不合规, 1: 合规
  same_quiz_issue: string
  same_quiz: number // 0: 不合规, 1: 合规
  product_length_issue: string
  product_length: number // 0: 不合规, 1: 合规
  joking_teacher_issue: string
  joking_teacher: number // 0: 不合规, 1: 合规
}

defineOptions({ name: 'ScriptAuditDetailDialog' })

const dialogVisible = ref(false) // 弹窗的是否展示
const detailData = ref<ScriptAuditVO | null>(null) // 详情数据

// 用于存储解析后的品牌规范数据
const brandRules = ref<BrandRule[]>([])
// 用于存储解析后的产品展示数据
const productDisplayResult = ref<ProductDisplayResult | null>(null)

/** 打开弹窗 */
const open = (data: ScriptAuditVO) => {
  detailData.value = data

  // 解析 result1 字段 (品牌规范)
  if (data.result1) {
    try {
      const parsedResult = JSON.parse(data.result1)
      brandRules.value = parsedResult.audit_results || []
    } catch (error) {
      console.error('解析品牌规范结果失败:', error)
      brandRules.value = [] // 解析失败则清空
    }
  } else {
    brandRules.value = [] // 如果 result1 不存在则清空
  }

  // 解析 result2 字段 (产品展示)
  if (data.result2) {
    try {
      productDisplayResult.value = JSON.parse(data.result2)
    } catch (error) {
      console.error('解析产品展示结果失败:', error)
      productDisplayResult.value = null // 解析失败则清空
    }
  } else {
    productDisplayResult.value = null // 如果 result2 不存在则清空
  }

  dialogVisible.value = true
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 格式化审核状态 */
const formatStatus = (status: string | undefined) => {
  if (status === '0') return '审核中'
  if (status === '1') return '审核完成'
  return ''
}

/** 获取状态标签类型 */
const getStatusTagType = (status: string | undefined) => {
  if (status === '0') return 'info'
  if (status === '1') return 'success'
  return ''
}
</script>

<style scoped>
.mb-4 {
  margin-bottom: 1rem; /* 添加下边距 */
}

.mb-2 {
  margin-bottom: 0.5rem; /* 添加下边距 */
}

.mt-1 {
  margin-top: 0.25rem; /* 添加上边距 */
}

.text-sm {
  font-size: 0.875rem; /* 调整字体大小 */
}

.text-gray-600 {
  color: #4b5563; /* 设置字体颜色 */
}

.rule-content {
  white-space: pre-wrap;
  word-break: break-all;
  padding: 4px;
  max-height: 100px;
  overflow-y: auto;
}
</style>
