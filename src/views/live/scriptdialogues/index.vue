<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="93px"
    >
      <el-form-item label="剧本id" prop="liveScriptId">
        <el-input
          v-model="queryParams.liveScriptId"
          placeholder="请输入剧本id"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="对话轮次" prop="roundNumber">
        <el-input
          v-model="queryParams.roundNumber"
          placeholder="请输入对话轮次"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="学生提问" prop="studentAnswer">
        <el-input
          v-model="queryParams.studentAnswer"
          placeholder="请输入学生提问"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="热词" prop="hotwords">
        <el-input
          v-model="queryParams.hotwords"
          placeholder="请输入热词"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="老师回答内容" prop="teacherResponse">
        <el-input
          v-model="queryParams.teacherResponse"
          placeholder="请输入老师回答内容"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="板书内容" prop="teacherResponseBlackboard">
        <el-input
          v-model="queryParams.teacherResponseBlackboard"
          placeholder="请输入老师提问板书内容"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="TTS音频地址" prop="teacherResponseTtsUrl">
        <el-input
          v-model="queryParams.teacherResponseTtsUrl"
          placeholder="请输入老师回应TTS音频地址"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['live:script-dialogues:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" />
          新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['live:script-dialogues:export']"
        >
          <Icon icon="ep:download" class="mr-5px" />
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="主键id" align="center" prop="id" />
      <el-table-column label="表live_script id" align="center" prop="liveScriptId" />
      <el-table-column label="对话轮次" align="center" prop="roundNumber" />
      <el-table-column label="学生提问" align="center" prop="studentAnswer" />
      <el-table-column label="老师回答内容" align="center" prop="teacherResponse" />
      <el-table-column label="老师提问板书内容" align="center" prop="teacherResponseBlackboard" />
      <el-table-column
        label="老师回应TTS语音内容"
        align="center"
        prop="teacherResponseTtsContent"
      />
      <el-table-column label="老师回应TTS音频地址" align="center" prop="teacherResponseTtsUrl" />
      <el-table-column label="热词" align="center" prop="hotwords" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['live:script-dialogues:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['live:script-dialogues:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ScriptDialoguesForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ScriptDialoguesApi, ScriptDialoguesVO } from '@/api/live/scriptdialogues'
import ScriptDialoguesForm from './ScriptDialoguesForm.vue'

/** 剧本对话信息 列表 */
defineOptions({ name: 'ScriptDialogues' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ScriptDialoguesVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  liveScriptId: undefined,
  roundNumber: undefined,
  studentAnswer: undefined,
  teacherResponse: undefined,
  teacherResponseBlackboard: undefined,
  teacherResponseTtsContent: undefined,
  teacherResponseTtsUrl: undefined,
  hotwords: undefined,
  remark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ScriptDialoguesApi.getScriptDialoguesPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ScriptDialoguesApi.deleteScriptDialogues(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ScriptDialoguesApi.exportScriptDialogues(queryParams)
    download.excel(data, '剧本对话信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
