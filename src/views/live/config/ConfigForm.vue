<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="700px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择类型">
          <el-option label="开场白配置" value="prologueTts" />
        </el-select>
      </el-form-item>
      <el-form-item label="适用编码列" prop="codes">
        <el-input
          v-model="formData.codes"
          placeholder="请输入适用编码列 如设备组编码，支持多个用逗号分割"
        />
      </el-form-item>
      <!-- 当类型为 prologueTts 时显示的字段 -->
      <template v-if="formData.type === 'prologueTts'">
        <div class="config-title">开场白配置</div>
        <el-card class="config-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>点击「家庭作业辅导」按钮</span>
            </div>
          </template>
          <el-form-item label="提示文本" prop="configFields.firstTts.ttsText">
            <el-input
              v-model="configFields.firstTts.ttsText"
              placeholder="请输入点击「家庭作业辅导」按钮的提示文本"
              type="textarea"
              :rows="2"
            />
          </el-form-item>
          <el-form-item label="TTS链接" prop="configFields.firstTts.ttsUrl">
            <el-input
              v-model="configFields.firstTts.ttsUrl"
              placeholder="请输入点击「家庭作业辅导」按钮的TTS链接"
            />
          </el-form-item>
        </el-card>

        <el-card class="config-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>进入作业辅导页面</span>
            </div>
          </template>
          <el-form-item label="提示文本" prop="configFields.secondTts.ttsText">
            <el-input
              v-model="configFields.secondTts.ttsText"
              placeholder="请输入进入作业辅导页面的提示文本"
              type="textarea"
              :rows="2"
            />
          </el-form-item>
          <el-form-item label="TTS链接" prop="configFields.secondTts.ttsUrl">
            <el-input
              v-model="configFields.secondTts.ttsUrl"
              placeholder="请输入进入作业辅导页面的TTS链接"
            />
          </el-form-item>
        </el-card>

        <el-card class="config-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>进入2.3.1B讲题</span>
            </div>
          </template>
          <el-form-item label="提示文本" prop="configFields.thirdTts.ttsText">
            <el-input
              v-model="configFields.thirdTts.ttsText"
              placeholder="请输入进入2.3.1B讲题的提示文本"
              type="textarea"
              :rows="2"
            />
          </el-form-item>
          <el-form-item label="TTS链接" prop="configFields.thirdTts.ttsUrl">
            <el-input
              v-model="configFields.thirdTts.ttsUrl"
              placeholder="请输入进入2.3.1B讲题的TTS链接"
            />
          </el-form-item>
        </el-card>
      </template>
      <!-- 其他类型显示原始 JSON 输入框 -->
      <el-form-item v-else label="配置JSON" prop="configJson">
        <el-input
          v-model="formData.configJson"
          placeholder="请输入配置JSON"
          type="textarea"
          :rows="5"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ConfigApi, ConfigVO } from '@/api/live/config'

/** 直播配置 表单 */
defineOptions({ name: 'ConfigForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改

// 配置字段对象，用于存储特定类型的配置字段
const configFields = ref({
  firstTts: {
    ttsText: '',
    ttsUrl: ''
  },
  secondTts: {
    ttsText: '',
    ttsUrl: ''
  },
  thirdTts: {
    ttsText: '',
    ttsUrl: ''
  }
})
const formData = ref({
  id: undefined,
  name: undefined,
  type: undefined,
  codes: undefined,
  configJson: undefined,
  remark: undefined
})
const formRules = reactive({
  name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '类型不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ConfigApi.getConfig(id)

      // 如果是 prologueTts 类型，解析 JSON 字符串并填充到表单字段中
      if (formData.value.type === 'prologueTts' && formData.value.configJson) {
        try {
          const parsedConfig = JSON.parse(formData.value.configJson)
          configFields.value = {
            firstTts: {
              ttsText: parsedConfig.firstTts?.ttsText || '',
              ttsUrl: parsedConfig.firstTts?.ttsUrl || ''
            },
            secondTts: {
              ttsText: parsedConfig.secondTts?.ttsText || '',
              ttsUrl: parsedConfig.secondTts?.ttsUrl || ''
            },
            thirdTts: {
              ttsText: parsedConfig.thirdTts?.ttsText || '',
              ttsUrl: parsedConfig.thirdTts?.ttsUrl || ''
            }
          }
        } catch (error) {
          console.error('Failed to parse configJson:', error)
          message.warning('配置JSON解析失败，请手动填写配置字段')
          // 重置配置字段
          configFields.value = {
            firstTts: {
              ttsText: '',
              ttsUrl: ''
            },
            secondTts: {
              ttsText: '',
              ttsUrl: ''
            },
            thirdTts: {
              ttsText: '',
              ttsUrl: ''
            }
          }
        }
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 如果是 prologueTts 类型，将配置字段序列化为 JSON 字符串
  if (formData.value.type === 'prologueTts') {
    formData.value.configJson = JSON.stringify(configFields.value)
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ConfigVO
    if (formType.value === 'create') {
      await ConfigApi.createConfig(data)
      message.success(t('common.createSuccess'))
    } else {
      await ConfigApi.updateConfig(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 监听类型变化，当类型变化时重置配置字段 */
watch(
  () => formData.value.type,
  (newType) => {
    if (newType === 'prologueTts') {
      // 如果已经有 configJson，尝试解析
      if (formData.value.configJson) {
        try {
          const parsedConfig = JSON.parse(formData.value.configJson)
          configFields.value = {
            firstTts: {
              ttsText: parsedConfig.firstTts?.ttsText || '',
              ttsUrl: parsedConfig.firstTts?.ttsUrl || ''
            },
            secondTts: {
              ttsText: parsedConfig.secondTts?.ttsText || '',
              ttsUrl: parsedConfig.secondTts?.ttsUrl || ''
            },
            thirdTts: {
              ttsText: parsedConfig.thirdTts?.ttsText || '',
              ttsUrl: parsedConfig.thirdTts?.ttsUrl || ''
            }
          }
        } catch (error) {
          // 解析失败，重置配置字段
          configFields.value = {
            firstTts: {
              ttsText: '',
              ttsUrl: ''
            },
            secondTts: {
              ttsText: '',
              ttsUrl: ''
            },
            thirdTts: {
              ttsText: '',
              ttsUrl: ''
            }
          }
        }
      } else {
        // 重置配置字段
        configFields.value = {
          firstTts: {
            ttsText: '',
            ttsUrl: ''
          },
          secondTts: {
            ttsText: '',
            ttsUrl: ''
          },
          thirdTts: {
            ttsText: '',
            ttsUrl: ''
          }
        }
      }
    }
  }
)

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    type: undefined,
    codes: undefined,
    configJson: undefined,
    remark: undefined
  }
  // 重置配置字段
  configFields.value = {
    firstTts: {
      ttsText: '',
      ttsUrl: ''
    },
    secondTts: {
      ttsText: '',
      ttsUrl: ''
    },
    thirdTts: {
      ttsText: '',
      ttsUrl: ''
    }
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.config-card {
  margin-bottom: 5px;
}

.config-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  color: #409eff;
  padding: 0;
  font-size: 13px;
}

/* 减小表单项的间距 */
.config-card :deep(.el-form-item) {
  margin-bottom: 10px;
}

.config-card :deep(.el-form-item:last-child) {
  margin-bottom: 0;
}

/* 减小卡片内部的padding */
.config-card :deep(.el-card__header) {
  padding: 5px 10px;
  min-height: auto;
}

.config-card :deep(.el-card__body) {
  padding: 10px;
}

/* 配置标题样式 */
.config-title {
  font-size: 14px;
  font-weight: bold;
  color: #606266;
  margin: 5px 0;
  text-align: center;
}

/* 调整表单项标签宽度 */
:deep(.el-form-item__label) {
  width: 70px;
  padding-right: 5px;
}

/* 调整弹窗宽度 */
:deep(.el-dialog) {
  max-width: 700px;
}
</style>
