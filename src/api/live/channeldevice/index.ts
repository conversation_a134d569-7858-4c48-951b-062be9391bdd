import request from '@/config/axios'

// 渠道设备关联 VO
export interface ChannelDeviceVO {
  id?: number // 主键id
  channelId: number // 渠道ID
  channelCode: string // 渠道编码
  deviceSn: string // 设备SN号
}

// 渠道设备关联 API
export const ChannelDeviceApi = {
  // 查询渠道设备关联分页
  getChannelDevicePage: async (params: any) => {
    return await request.get({ url: `/live/channel-device/page`, params })
  },

  // 查询渠道设备关联详情
  getChannelDevice: async (id: number) => {
    return await request.get({ url: `/live/channel-device/get?id=` + id })
  },

  // 新增渠道设备关联
  createChannelDevice: async (data: ChannelDeviceVO) => {
    return await request.post({ url: `/live/channel-device/create`, data })
  },

  // 修改渠道设备关联
  updateChannelDevice: async (data: ChannelDeviceVO) => {
    return await request.put({ url: `/live/channel-device/update`, data })
  },

  // 删除渠道设备关联
  deleteChannelDevice: async (id: number) => {
    return await request.delete({ url: `/live/channel-device/delete?id=` + id })
  },

  // 导出渠道设备关联 Excel
  exportChannelDevice: async (params) => {
    return await request.download({ url: `/live/channel-device/export-excel`, params })
  }
}
