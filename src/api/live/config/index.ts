import request from '@/config/axios'

// 直播配置 VO
export interface ConfigVO {
  id: number // 主键id
  name: string // 名称
  type: string // 类型
  codes: string // 适用编码列
  configJson: string // 配置JSON
  remark: string // 备注
}

// 直播配置 API
export const ConfigApi = {
  // 查询直播配置分页
  getConfigPage: async (params: any) => {
    return await request.get({ url: `/live/config/page`, params })
  },

  // 查询直播配置详情
  getConfig: async (id: number) => {
    return await request.get({ url: `/live/config/get?id=` + id })
  },

  // 新增直播配置
  createConfig: async (data: ConfigVO) => {
    return await request.post({ url: `/live/config/create`, data })
  },

  // 修改直播配置
  updateConfig: async (data: ConfigVO) => {
    return await request.put({ url: `/live/config/update`, data })
  },

  // 删除直播配置
  deleteConfig: async (id: number) => {
    return await request.delete({ url: `/live/config/delete?id=` + id })
  },

  // 导出直播配置 Excel
  exportConfig: async (params) => {
    return await request.download({ url: `/live/config/export-excel`, params })
  }
}
