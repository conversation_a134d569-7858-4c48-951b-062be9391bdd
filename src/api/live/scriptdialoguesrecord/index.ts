import request from '@/config/axios'

// 用户剧本调用记录 VO
export interface ScriptDialoguesRecordVO {
  id: number // 主键id
  liveScriptId: number // 表live_script的 id
  liveScriptDialoguesId: number // 表live_script_dialogues 的id
  roundNumber: number // 对话轮次
  studentRequest: string // 学生提问
  teacherResponse: string // 老师回应内容
  teacherResponseBlackboard: string // 老师回应板书内容
  teacherResponseTtsContent: string // 老师回应TTS语音内容
  teacherResponseTtsUrl: string // 老师回应TTS音频地址
  hotwords: string // 热词
  roomId: number // 房间id
  roundId: number // 轮次ID
  userId: number // 用户ID
  remark: string // 备注
}

// 用户剧本调用记录 API
export const ScriptDialoguesRecordApi = {
  // 查询用户剧本调用记录分页
  getScriptDialoguesRecordPage: async (params: any) => {
    return await request.get({ url: `/live/script-dialogues-record/page`, params })
  },

  // 查询用户剧本调用记录详情
  getScriptDialoguesRecord: async (id: number) => {
    return await request.get({ url: `/live/script-dialogues-record/get?id=` + id })
  },

  // 新增用户剧本调用记录
  createScriptDialoguesRecord: async (data: ScriptDialoguesRecordVO) => {
    return await request.post({ url: `/live/script-dialogues-record/create`, data })
  },

  // 修改用户剧本调用记录
  updateScriptDialoguesRecord: async (data: ScriptDialoguesRecordVO) => {
    return await request.put({ url: `/live/script-dialogues-record/update`, data })
  },

  // 删除用户剧本调用记录
  deleteScriptDialoguesRecord: async (id: number) => {
    return await request.delete({ url: `/live/script-dialogues-record/delete?id=` + id })
  },

  // 导出用户剧本调用记录 Excel
  exportScriptDialoguesRecord: async (params) => {
    return await request.download({ url: `/live/script-dialogues-record/export-excel`, params })
  }
}