<template>
  <div class="app-wrapper">
    <!-- 侧边栏 -->
    <div class="sidebar-container">
      <el-scrollbar>
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :background-color="variables.menuBg"
          :text-color="variables.menuText"
          :active-text-color="variables.menuActiveText"
          :unique-opened="false"
          :collapse-transition="false"
          mode="vertical"
        >
          <sidebar-item
            v-for="route in routes"
            :key="route.path"
            :item="route"
            :base-path="route.path"
          />
        </el-menu>
      </el-scrollbar>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
      <!-- 头部 -->
      <div class="navbar">
        <hamburger :is-active="sidebar.opened" @toggleClick="toggleSideBar" />
        <breadcrumb />
        <div class="right-menu">
          <el-dropdown class="avatar-container" trigger="click">
            <div class="avatar-wrapper">
              <el-avatar :size="30" :src="userStore.avatar" />
              <span class="user-name">{{ userStore.name }}</span>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 内容区 -->
      <div class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive>
              <component :is="Component" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'
import SidebarItem from './components/SidebarItem.vue'
import Hamburger from './components/Hamburger.vue'
import Breadcrumb from './components/Breadcrumb.vue'
import variables from '@/styles/variables.module.scss'

const route = useRoute()
const appStore = useAppStore()
const userStore = useUserStore()

// 计算属性
const sidebar = computed(() => appStore.sidebar)
const routes = computed(() => appStore.routes)
const activeMenu = computed(() => route.path)
const isCollapse = computed(() => !sidebar.value.opened)

// 方法
const toggleSideBar = () => {
  appStore.toggleSideBar()
}

const handleLogout = () => {
  userStore.logout()
}
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;

  .sidebar-container {
    width: 210px;
    height: 100%;
    background-color: $menuBg;
    transition: width 0.28s;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;

    &.is-collapse {
      width: 64px;
    }
  }

  .main-container {
    min-height: 100%;
    transition: margin-left 0.28s;
    margin-left: 210px;
    position: relative;
    flex: 1;

    .navbar {
      height: 50px;
      overflow: hidden;
      position: relative;
      background: #fff;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
      display: flex;
      align-items: center;
      padding: 0 15px;

      .right-menu {
        margin-left: auto;
        display: flex;
        align-items: center;

        .avatar-container {
          cursor: pointer;
          display: flex;
          align-items: center;

          .avatar-wrapper {
            display: flex;
            align-items: center;

            .user-name {
              margin-left: 8px;
              font-size: 14px;
            }
          }
        }
      }
    }

    .app-main {
      min-height: calc(100vh - 50px);
      padding: 20px;
      background-color: #f0f2f5;
    }
  }
}

// 过渡动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style> 